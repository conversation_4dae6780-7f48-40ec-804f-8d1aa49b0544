"""
速率限制中间件
"""
import time
import asyncio
from typing import Dict, Optional, Any
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from src.core.config import settings
from src.core.security import security_service
import logging

logger = logging.getLogger(__name__)

class RateLimiter:
    """速率限制器"""
    
    def __init__(self):
        self.requests: Dict[str, Dict[str, Any]] = {}
        self.cleanup_task = None
        self.default_limits = {
            "requests_per_minute": 60,
            "requests_per_hour": 1000,
            "requests_per_day": 10000
        }
    
    async def check_rate_limit(
        self, 
        request: Request, 
        limits: Optional[Dict[str, int]] = None
    ) -> bool:
        """
        检查速率限制
        
        Args:
            request: FastAPI请求对象
            limits: 自定义限制配置
            
        Returns:
            是否允许请求
        """
        # 获取客户端标识符
        client_id = await self._get_client_id(request)
        
        # 使用默认限制或自定义限制
        rate_limits = limits or self.default_limits
        
        # 获取当前时间
        current_time = time.time()
        
        # 初始化客户端数据
        if client_id not in self.requests:
            self.requests[client_id] = {
                "timestamps": [],
                "minute_count": 0,
                "hour_count": 0,
                "day_count": 0,
                "last_cleanup": current_time
            }
        
        client_data = self.requests[client_id]
        
        # 清理过期记录
        await self._cleanup_expired_requests(client_data, current_time)
        
        # 检查各时间窗口的限制
        if not await self._check_window_limits(client_data, rate_limits, current_time):
            return False
        
        # 记录请求
        self._record_request(client_data, current_time)
        
        return True
    
    async def _get_client_id(self, request: Request) -> str:
        """
        获取客户端标识符
        
        Args:
            request: FastAPI请求对象
            
        Returns:
            客户端标识符
        """
        # 尝试从请求头获取API密钥
        api_key = request.headers.get("X-API-Key")
        if api_key:
            return f"api_key:{api_key[:16]}"
        
        # 尝试从Authorization头获取用户信息
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header[7:]
            try:
                # 验证令牌获取用户信息
                payload = security_service.verify_token(token)
                user_id = payload.get("sub", "anonymous")
                return f"user:{user_id}"
            except:
                pass
        
        # 使用IP地址作为最后手段
        client_ip = request.client.host if request.client else "unknown"
        return f"ip:{client_ip}"
    
    async def _cleanup_expired_requests(self, client_data: Dict[str, Any], current_time: float):
        """清理过期的请求记录"""
        last_cleanup = client_data.get("last_cleanup", 0)
        
        # 每分钟清理一次
        if current_time - last_cleanup < 60:
            return
        
        # 清理1小时前的记录
        client_data["timestamps"] = [
            timestamp for timestamp in client_data["timestamps"]
            if current_time - timestamp < 3600
        ]
        
        # 重置计数器
        client_data["minute_count"] = len([
            timestamp for timestamp in client_data["timestamps"]
            if current_time - timestamp < 60
        ])
        
        client_data["hour_count"] = len([
            timestamp for timestamp in client_data["timestamps"]
            if current_time - timestamp < 3600
        ])
        
        client_data["day_count"] = len([
            timestamp for timestamp in client_data["timestamps"]
            if current_time - timestamp < 86400
        ])
        
        client_data["last_cleanup"] = current_time
    
    async def _check_window_limits(
        self, 
        client_data: Dict[str, Any], 
        limits: Dict[str, int], 
        current_time: float
    ) -> bool:
        """检查各时间窗口的限制"""
        # 检查分钟限制
        if client_data["minute_count"] >= limits.get("requests_per_minute", 60):
            return False
        
        # 检查小时限制
        if client_data["hour_count"] >= limits.get("requests_per_hour", 1000):
            return False
        
        # 检查天限制
        if client_data["day_count"] >= limits.get("requests_per_day", 10000):
            return False
        
        return True
    
    def _record_request(self, client_data: Dict[str, Any], current_time: float):
        """记录请求"""
        client_data["timestamps"].append(current_time)
        client_data["minute_count"] += 1
        client_data["hour_count"] += 1
        client_data["day_count"] += 1
    
    def get_remaining_requests(self, client_id: str) -> Dict[str, int]:
        """
        获取剩余请求数
        
        Args:
            client_id: 客户端标识符
            
        Returns:
            各时间窗口的剩余请求数
        """
        if client_id not in self.requests:
            return {
                "remaining_minute": self.default_limits["requests_per_minute"],
                "remaining_hour": self.default_limits["requests_per_hour"],
                "remaining_day": self.default_limits["requests_per_day"]
            }
        
        client_data = self.requests[client_id]
        current_time = time.time()
        
        # 计算剩余请求数
        remaining_minute = max(
            0, 
            self.default_limits["requests_per_minute"] - client_data["minute_count"]
        )
        remaining_hour = max(
            0, 
            self.default_limits["requests_per_hour"] - client_data["hour_count"]
        )
        remaining_day = max(
            0, 
            self.default_limits["requests_per_day"] - client_data["day_count"]
        )
        
        return {
            "remaining_minute": remaining_minute,
            "remaining_hour": remaining_hour,
            "remaining_day": remaining_day
        }
    
    def reset_limits(self, client_id: str):
        """重置客户端的速率限制"""
        if client_id in self.requests:
            del self.requests[client_id]

class RateLimitMiddleware:
    """速率限制中间件"""
    
    def __init__(self, app):
        self.app = app
        self.rate_limiter = RateLimiter()
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        # 创建请求对象
        request = Request(scope, receive)
        
        # 跳过速率限制的路径
        if await self._should_skip_rate_limit(request):
            await self.app(scope, receive, send)
            return
        
        # 检查速率限制
        limits = await self._get_rate_limits(request)
        
        if not await self.rate_limiter.check_rate_limit(request, limits):
            # 获取客户端ID用于日志
            client_id = await self.rate_limiter._get_client_id(request)
            logger.warning(f"速率限制触发: {client_id}")
            
            # 返回429状态码
            response = JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "error": "请求过于频繁",
                    "message": "您的请求过于频繁，请稍后再试",
                    "retry_after": 60
                }
            )
            
            await response(scope, receive, send)
            return
        
        # 添加速率限制头信息
        async def send_with_rate_headers(message):
            if message["type"] == "http.response.start":
                client_id = await self.rate_limiter._get_client_id(request)
                remaining = self.rate_limiter.get_remaining_requests(client_id)
                
                headers = dict(message.get("headers", []))
                headers.update({
                    b"X-RateLimit-Limit-Minute": str(self.rate_limiter.default_limits["requests_per_minute"]).encode(),
                    b"X-RateLimit-Remaining-Minute": str(remaining["remaining_minute"]).encode(),
                    b"X-RateLimit-Limit-Hour": str(self.rate_limiter.default_limits["requests_per_hour"]).encode(),
                    b"X-RateLimit-Remaining-Hour": str(remaining["remaining_hour"]).encode(),
                    b"X-RateLimit-Limit-Day": str(self.rate_limiter.default_limits["requests_per_day"]).encode(),
                    b"X-RateLimit-Remaining-Day": str(remaining["remaining_day"]).encode(),
                })
                
                message["headers"] = list(headers.items())
            
            await send(message)
        
        await self.app(scope, receive, send_with_rate_headers)
    
    async def _should_skip_rate_limit(self, request: Request) -> bool:
        """判断是否跳过速率限制"""
        # 跳过的路径
        skip_paths = [
            "/docs",
            "/redoc",
            "/openapi.json",
            "/health",
            "/static/",
            "/api/v1/auth/login",
            "/api/v1/test"
        ]
        
        for path in skip_paths:
            if request.url.path.startswith(path):
                return True
        
        return False
    
    async def _get_rate_limits(self, request: Request) -> Dict[str, int]:
        """获取速率限制配置"""
        # 根据不同的用户角色或API密钥类型返回不同的限制
        api_key = request.headers.get("X-API-Key")
        
        if api_key:
            # API密钥用户可以有更高的限制
            return {
                "requests_per_minute": 120,
                "requests_per_hour": 2000,
                "requests_per_day": 20000
            }
        
        # 默认限制
        return self.rate_limiter.default_limits

# 创建速率限制器实例
rate_limiter = RateLimiter()