<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档分析系统 - 智能政策文档分析平台</title>
    <link rel="stylesheet" href="style.css">
    <meta name="description" content="基于智谱AI的智能文档分析系统，提供政策文档深度分析、行为者关系识别、因果机制分析等功能">
    <meta name="keywords" content="文档分析, 政策分析, AI分析, 智谱AI, 文本挖掘">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
        // 主题切换功能
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            const icon = document.querySelector('.theme-toggle i');
            if (newTheme === 'dark') {
                icon.textContent = '☀️';
            } else {
                icon.textContent = '🌙';
            }
        }
        
        // 初始化主题
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            
            const icon = document.querySelector('.theme-toggle i');
            if (icon && savedTheme === 'dark') {
                icon.textContent = '☀️';
            }
        });
    </script>
</head>
<body>
    <div class="container">
        <header>
            <h1>📄 智能文档分析系统</h1>
            <p class="subtitle">基于智谱AI的政策文档深度分析工具</p>
            <nav class="main-nav">
                <a href="/" class="nav-link active">🏠 文档分析</a>
                <a href="/static/visualization.html" class="nav-link">📊 数据可视化</a>
                <a href="/static/prompts.html" class="nav-link">🔧 提示词编辑器</a>
                <a href="/docs" class="nav-link">📚 API文档</a>
                <button class="theme-toggle" onclick="toggleTheme()" title="切换主题">
                    <i>🌙</i>
                </button>
            </nav>
        </header>

        <main>
            <!-- API配置区域 -->
            <section class="config-section">
                <h2>⚙️ API配置</h2>
                <div class="form-group">
                    <label for="api-key">智谱AI API密钥</label>
                    <input type="password" id="api-key" placeholder="请输入您的智谱AI API密钥（可选，服务器端已配置则无需填写）">
                    <small class="hint">如需获取API密钥，请访问 <a href="https://open.bigmodel.cn/" target="_blank">智谱AI开放平台</a></small>
                </div>
                <div class="form-group">
                    <label for="base-url">API Base URL</label>
                    <input type="text" id="base-url" placeholder="默认: https://open.bigmodel.cn/api/paas/v4">
                    <small class="hint">如果您需要使用自定义的智谱AI端点，请在此输入</small>
                </div>
            </section>

            <!-- 文档输入区域 -->
            <section class="input-section">
                <h2>📝 输入文档</h2>
                
                <!-- 文件上传选项 -->
                <div class="upload-section">
                    <div class="upload-buttons">
                        <label for="file-upload" class="upload-btn">
                            📁 上传文件
                            <input type="file" id="file-upload" accept=".txt,.md,.doc,.docx,.pdf" style="display: none;">
                        </label>
                        <label for="folder-upload" class="upload-btn">
                            📂 上传文件夹
                            <input type="file" id="folder-upload" webkitdirectory directory multiple style="display: none;">
                        </label>
                    </div>
                    <div id="file-list" class="file-list"></div>
                </div>
                
                <div class="divider">或</div>
                
                <!-- 手动输入 -->
                <div class="form-group">
                    <label for="doc-title">文档标题</label>
                    <input type="text" id="doc-title" placeholder="请输入文档标题">
                </div>
                <div class="form-group">
                    <label for="doc-content">文档内容</label>
                    <textarea id="doc-content" rows="10" placeholder="请粘贴或输入文档内容..."></textarea>
                </div>
                <div class="form-group">
                    <label>选择分析任务</label>
                    <div class="task-options">
                        <label class="task-option">
                            <input type="checkbox" name="task" value="actor_relation" checked>
                            <span>行为者与关系分析</span>
                        </label>
                        <label class="task-option">
                            <input type="checkbox" name="task" value="role_framing">
                            <span>角色塑造检测</span>
                        </label>
                        <label class="task-option">
                            <input type="checkbox" name="task" value="problem_scope">
                            <span>问题范围策略</span>
                        </label>
                        <label class="task-option">
                            <input type="checkbox" name="task" value="causal_mechanism">
                            <span>因果机制分析</span>
                        </label>
                    </div>
                </div>
                <button id="analyze-btn" class="primary-btn">🚀 开始分析</button>
            </section>

            <!-- 结果显示区域 -->
            <section class="results-section" id="results-section" style="display: none;">
                <h2>📊 分析结果</h2>
                <div id="loading" class="loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>正在分析文档，请稍候...</p>
                </div>
                <div id="results-container"></div>
            </section>

            <!-- 历史记录区域 -->
            <section class="history-section">
                <h2>📚 历史记录</h2>
                <div id="history-list" class="history-list">
                    <p class="empty-state">暂无历史记录</p>
                </div>
            </section>
        </main>

        <footer>
            <p>&copy; 2024 文档分析系统 | Powered by 智谱AI</p>
        </footer>
    </div>

    <script src="app.js"></script>
</body>
</html>
