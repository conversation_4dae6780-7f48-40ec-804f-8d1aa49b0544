<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据可视化 - 文档分析系统</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns"></script>
    <style>
        .visualization-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .viz-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .viz-controls {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .summary-card .value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .summary-card .label {
            color: #666;
            font-size: 0.9em;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .chart-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            text-align: center;
        }
        
        .chart-wrapper {
            position: relative;
            height: 400px;
        }
        
        .network-chart {
            height: 500px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state svg {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            opacity: 0.3;
        }
        
        .task-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .task-tab {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .task-tab.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .task-tab:hover {
            border-color: #667eea;
        }
        
        .export-controls {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .control-row {
                grid-template-columns: 1fr;
            }
            
            .summary-cards {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="visualization-container">
        <div class="viz-header">
            <h1>📊 分析结果可视化</h1>
            <p>将复杂的分析结果转换为直观的图表展示</p>
            <nav class="main-nav">
                <a href="/" class="nav-link">🏠 文档分析</a>
                <a href="/static/prompts.html" class="nav-link">🔧 提示词编辑器</a>
                <a href="/static/visualization.html" class="nav-link active">📊 可视化</a>
                <a href="/docs" class="nav-link">📚 API文档</a>
            </nav>
        </div>

        <!-- 控制面板 -->
        <div class="viz-controls">
            <div class="control-row">
                <div class="control-group">
                    <label for="doc-id">文档ID:</label>
                    <input type="text" id="doc-id" placeholder="输入文档ID" value="sample_doc_001">
                </div>
                <div class="control-group">
                    <label for="task-filter">分析任务:</label>
                    <select id="task-filter" multiple>
                        <option value="actor_relation">行为者关系</option>
                        <option value="role_framing">角色塑造</option>
                        <option value="problem_scope">问题范围</option>
                        <option value="causal_mechanism">因果机制</option>
                    </select>
                </div>
            </div>
            <div class="control-group">
                <button class="btn btn-primary" onclick="loadVisualization()">🔄 加载可视化</button>
                <button class="btn btn-secondary" onclick="loadSampleData()">📋 加载示例数据</button>
            </div>
        </div>

        <!-- 摘要卡片 -->
        <div class="summary-cards" id="summary-cards">
            <div class="summary-card">
                <div class="value" id="total-actors">-</div>
                <div class="label">行为者总数</div>
            </div>
            <div class="summary-card">
                <div class="value" id="total-relations">-</div>
                <div class="label">关系总数</div>
            </div>
            <div class="summary-card">
                <div class="value" id="complexity-score">-</div>
                <div class="label">复杂度评分</div>
            </div>
            <div class="summary-card">
                <div class="value" id="task-count">-</div>
                <div class="label">分析任务数</div>
            </div>
        </div>

        <!-- 任务标签 -->
        <div class="task-tabs" id="task-tabs"></div>

        <!-- 图表区域 -->
        <div class="charts-grid" id="charts-container">
            <div class="loading">
                <div class="spinner"></div>
                <p>请选择文档并加载可视化数据</p>
            </div>
        </div>

        <!-- 导出控制 -->
        <div class="export-controls">
            <button class="btn btn-secondary" onclick="exportData('json')">📄 导出JSON</button>
            <button class="btn btn-secondary" onclick="exportData('csv')">📊 导出CSV</button>
        </div>
    </div>

    <script>
        let currentData = null;
        let charts = {};

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            
            // 检查是否从分析页面跳转过来
            const fromAnalysis = sessionStorage.getItem('fromAnalysis');
            const currentDocId = sessionStorage.getItem('currentDocId');
            
            if (fromAnalysis === 'true' && currentDocId) {
                // 自动加载当前文档的可视化
                document.getElementById('doc-id').value = currentDocId;
                loadVisualization();
                
                // 清除sessionStorage中的标记
                sessionStorage.removeItem('fromAnalysis');
                sessionStorage.removeItem('currentDocId');
            } else {
                // 否则加载示例数据
                loadSampleData();
            }
        });

        function initializeEventListeners() {
            // 设置任务筛选器事件
            document.getElementById('task-filter').addEventListener('change', function() {
                if (currentData) {
                    renderVisualization(currentData);
                }
            });
        }

        async function loadVisualization() {
            const docId = document.getElementById('doc-id').value.trim();
            if (!docId) {
                alert('请输入文档ID');
                return;
            }

            showLoading();
            
            try {
                const taskFilter = Array.from(document.getElementById('task-filter').selectedOptions)
                    .map(option => option.value);
                
                const params = new URLSearchParams();
                if (taskFilter.length > 0) {
                    taskFilter.forEach(task => params.append('task_types', task));
                }

                const response = await fetch(`/api/v1/visualization/data/${docId}?${params}`);
                if (response.ok) {
                    currentData = await response.json();
                    renderVisualization(currentData);
                } else {
                    const error = await response.json();
                    showError(`加载可视化数据失败: ${error.detail || '未知错误'}`);
                }
            } catch (error) {
                console.error('加载可视化数据失败:', error);
                showError('加载可视化数据失败');
            }
        }

        function loadSampleData() {
            // 加载示例数据用于演示
            const sampleData = {
                doc_id: "sample_doc_001",
                title: "示例政策文档",
                analysis_date: new Date().toISOString(),
                task_types: ["actor_relation", "role_framing"],
                charts: {
                    actor_relation: [
                        {
                            type: "pie",
                            title: "行为者类型分布",
                            data: {
                                labels: ["政府机构", "企业", "公众", "社会组织"],
                                datasets: [{
                                    data: [5, 3, 2, 1],
                                    backgroundColor: ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0"]
                                }]
                            }
                        },
                        {
                            type: "bar",
                            title: "关系类型分布",
                            data: {
                                labels: ["监管关系", "合作关系", "对立关系", "服务关系"],
                                datasets: [{
                                    label: "关系数量",
                                    data: [8, 4, 2, 3],
                                    backgroundColor: "#36A2EB"
                                }]
                            }
                        }
                    ],
                    role_framing: [
                        {
                            type: "pie",
                            title: "角色分布",
                            data: {
                                labels: ["英雄", "受害者", "反派"],
                                datasets: [{
                                    data: [3, 2, 1],
                                    backgroundColor: ["#28a745", "#ffc107", "#dc3545"]
                                }]
                            }
                        },
                        {
                            type: "radar",
                            title: "角色特征分析",
                            data: {
                                labels: ["积极性", "影响力", "描述详细度", "情感强度"],
                                datasets: [
                                    {
                                        label: "英雄",
                                        data: [85, 75, 80, 90],
                                        borderColor: "#28a745",
                                        backgroundColor: "rgba(40, 167, 69, 0.2)"
                                    },
                                    {
                                        label: "受害者",
                                        data: [30, 60, 70, 40],
                                        borderColor: "#ffc107",
                                        backgroundColor: "rgba(255, 193, 7, 0.2)"
                                    }
                                ]
                            }
                        }
                    ]
                },
                summary: {
                    total_actors: 11,
                    total_relations: 17,
                    complexity_score: 0.8,
                    key_themes: ["政策监管", "企业发展", "公众参与"]
                }
            };
            
            currentData = sampleData;
            renderVisualization(currentData);
        }

        function renderVisualization(data) {
            updateSummaryCards(data.summary);
            updateTaskTabs(data.task_types);
            renderCharts(data.charts);
        }

        function updateSummaryCards(summary) {
            document.getElementById('total-actors').textContent = summary.total_actors || 0;
            document.getElementById('total-relations').textContent = summary.total_relations || 0;
            document.getElementById('complexity-score').textContent = 
                ((summary.complexity_score || 0) * 100).toFixed(0) + '%';
            document.getElementById('task-count').textContent = 
                Object.keys(currentData.charts || {}).length;
        }

        function updateTaskTabs(taskTypes) {
            const container = document.getElementById('task-tabs');
            const selectedTasks = Array.from(document.getElementById('task-filter').selectedOptions)
                .map(option => option.value);
            
            const tasksToShow = selectedTasks.length > 0 ? selectedTasks : taskTypes;
            
            container.innerHTML = tasksToShow.map(task => `
                <div class="task-tab active" data-task="${task}">
                    ${getTaskTypeName(task)}
                </div>
            `).join('');
        }

        function renderCharts(chartsData) {
            const container = document.getElementById('charts-container');
            const selectedTasks = Array.from(document.getElementById('task-filter').selectedOptions)
                .map(option => option.value);
            
            const tasksToShow = selectedTasks.length > 0 ? selectedTasks : Object.keys(chartsData);
            
            // 清除现有图表
            Object.values(charts).forEach(chart => chart.destroy());
            charts = {};
            
            let html = '';
            
            tasksToShow.forEach(taskType => {
                const taskCharts = chartsData[taskType] || [];
                taskCharts.forEach((chartConfig, index) => {
                    const chartId = `chart-${taskType}-${index}`;
                    html += `
                        <div class="chart-container">
                            <div class="chart-title">${chartConfig.title}</div>
                            <div class="chart-wrapper">
                                <canvas id="${chartId}"></canvas>
                            </div>
                        </div>
                    `;
                });
            });
            
            if (html === '') {
                html = `
                    <div class="empty-state">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4zm2.5 2.1h-15V5h15v14.1zm0-16.1h-15c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h15c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"/>
                        </svg>
                        <h3>暂无图表数据</h3>
                        <p>请选择分析任务或检查文档分析结果</p>
                    </div>
                `;
            }
            
            container.innerHTML = html;
            
            // 渲染图表
            setTimeout(() => {
                tasksToShow.forEach(taskType => {
                    const taskCharts = chartsData[taskType] || [];
                    taskCharts.forEach((chartConfig, index) => {
                        const chartId = `chart-${taskType}-${index}`;
                        const canvas = document.getElementById(chartId);
                        if (canvas) {
                            charts[chartId] = createChart(canvas, chartConfig);
                        }
                    });
                });
            }, 100);
        }

        function createChart(canvas, config) {
            const ctx = canvas.getContext('2d');
            
            const chartConfig = {
                type: config.type,
                data: config.data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: false
                        }
                    }
                }
            };

            // 特殊配置
            if (config.type === 'radar') {
                chartConfig.options.scales = {
                    r: {
                        beginAtZero: true,
                        max: 100
                    }
                };
            } else if (config.type === 'bar') {
                chartConfig.options.scales = {
                    y: {
                        beginAtZero: true
                    }
                };
            }

            return new Chart(ctx, chartConfig);
        }

        function getTaskTypeName(taskType) {
            const names = {
                'actor_relation': '行为者关系',
                'role_framing': '角色塑造',
                'problem_scope': '问题范围',
                'causal_mechanism': '因果机制'
            };
            return names[taskType] || taskType;
        }

        function showLoading() {
            document.getElementById('charts-container').innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>正在加载可视化数据...</p>
                </div>
            `;
        }

        function showError(message) {
            document.getElementById('charts-container').innerHTML = `
                <div class="error-message">
                    <strong>错误:</strong> ${message}
                </div>
            `;
        }

        async function exportData(format) {
            if (!currentData) {
                alert('没有可导出的数据');
                return;
            }

            try {
                const response = await fetch(`/api/v1/visualization/export/${currentData.doc_id}?format=${format}`);
                if (response.ok) {
                    const result = await response.json();
                    
                    if (format === 'json') {
                        const blob = new Blob([JSON.stringify(result.data, null, 2)], { type: 'application/json' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `visualization_${currentData.doc_id}.json`;
                        a.click();
                        URL.revokeObjectURL(url);
                    } else if (format === 'csv') {
                        const blob = new Blob([result.data], { type: 'text/csv' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `visualization_${currentData.doc_id}.csv`;
                        a.click();
                        URL.revokeObjectURL(url);
                    }
                    
                    alert('导出成功');
                } else {
                    alert('导出失败');
                }
            } catch (error) {
                console.error('导出失败:', error);
                alert('导出失败');
            }
        }
    </script>
</body>
</html>