// 全局变量
let historyData = [];
let uploadedFiles = [];

// DOM元素
const analyzeBtn = document.getElementById('analyze-btn');
const docTitle = document.getElementById('doc-title');
const docContent = document.getElementById('doc-content');
const resultsSection = document.getElementById('results-section');
const resultsContainer = document.getElementById('results-container');
const loading = document.getElementById('loading');
const historyList = document.getElementById('history-list');
const fileUpload = document.getElementById('file-upload');
const folderUpload = document.getElementById('folder-upload');
const fileList = document.getElementById('file-list');
const apiKeyInput = document.getElementById('api-key');
const baseUrlInput = document.getElementById('base-url');

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    loadHistory();
    analyzeBtn.addEventListener('click', analyzeDocument);
    fileUpload.addEventListener('change', handleFileUpload);
    folderUpload.addEventListener('change', handleFolderUpload);
    
    // 加载保存的API密钥
    const savedApiKey = localStorage.getItem('zhipuApiKey');
    if (savedApiKey) {
        apiKeyInput.value = savedApiKey;
    }
    
    // 加载保存的Base URL
    const savedBaseUrl = localStorage.getItem('zhipuBaseUrl');
    if (savedBaseUrl) {
        baseUrlInput.value = savedBaseUrl;
    }
});

// 处理单文件上传
async function handleFileUpload(event) {
    const file = event.target.files[0];
    if (file) {
        await processFile(file);
    }
}

// 处理文件夹上传
async function handleFolderUpload(event) {
    const files = Array.from(event.target.files);
    uploadedFiles = [];
    fileList.innerHTML = '';
    
    for (const file of files) {
        // 只处理文本文件
        if (file.name.endsWith('.txt') || file.name.endsWith('.md')) {
            await processFile(file, true);
        }
    }
    
    if (uploadedFiles.length > 0) {
        showMessage(`已加载 ${uploadedFiles.length} 个文件`, 'success');
    }
}

// 处理文件
async function processFile(file, isMultiple = false) {
    const reader = new FileReader();
    
    return new Promise((resolve) => {
        reader.onload = (e) => {
            const content = e.target.result;
            const fileName = file.name;
            const title = fileName.replace(/\.[^/.]+$/, ''); // 移除扩展名作为标题
            
            if (isMultiple) {
                // 多文件模式，添加到列表
                uploadedFiles.push({ title, content, fileName });
                addFileToList(fileName, uploadedFiles.length - 1);
            } else {
                // 单文件模式，直接填充到表单
                docTitle.value = title;
                docContent.value = content;
                showMessage(`文件 "${fileName}" 已加载`, 'success');
            }
            resolve();
        };
        
        reader.onerror = () => {
            showMessage(`无法读取文件: ${file.name}`, 'error');
            resolve();
        };
        
        reader.readAsText(file, 'UTF-8');
    });
}

// 添加文件到列表显示
function addFileToList(fileName, index) {
    const fileItem = document.createElement('div');
    fileItem.className = 'file-item';
    fileItem.innerHTML = `
        <span class="file-name">${fileName}</span>
        <span class="remove-btn" onclick="removeFile(${index})">×</span>
    `;
    fileItem.onclick = () => selectFile(index);
    fileList.appendChild(fileItem);
}

// 选择文件
function selectFile(index) {
    const file = uploadedFiles[index];
    if (file) {
        docTitle.value = file.title;
        docContent.value = file.content;
        showMessage(`已选择: ${file.fileName}`, 'success');
    }
}

// 移除文件
function removeFile(index) {
    uploadedFiles.splice(index, 1);
    updateFileList();
}

// 更新文件列表显示
function updateFileList() {
    fileList.innerHTML = '';
    uploadedFiles.forEach((file, index) => {
        addFileToList(file.fileName, index);
    });
}

// 分析文档
async function analyzeDocument() {
    // 保存API密钥（如果提供）
    const apiKey = apiKeyInput.value.trim();
    if (apiKey) {
        localStorage.setItem('zhipuApiKey', apiKey);
    }
    
    // 保存Base URL（如果提供）
    const baseUrl = baseUrlInput.value.trim();
    if (baseUrl) {
        localStorage.setItem('zhipuBaseUrl', baseUrl);
    }
    
    // 获取输入数据
    const title = docTitle.value.trim();
    const content = docContent.value.trim();
    
    // 验证输入
    if (!title) {
        showMessage('请输入文档标题', 'error');
        return;
    }
    
    if (!content) {
        showMessage('请输入文档内容', 'error');
        return;
    }
    
    // 获取选中的任务
    const tasks = [];
    document.querySelectorAll('input[name="task"]:checked').forEach(checkbox => {
        tasks.push(checkbox.value);
    });
    
    if (tasks.length === 0) {
        showMessage('请至少选择一个分析任务', 'error');
        return;
    }
    
    // 显示加载状态
    resultsSection.style.display = 'block';
    loading.style.display = 'block';
    resultsContainer.innerHTML = '';
    
    try {
        // 生成文档ID
        const docId = `doc_${Date.now()}`;
        
        // 构建请求头
        const headers = {
            'Content-Type': 'application/json'
        };
        
        // 如果提供了API密钥，添加到请求头
        if (apiKey) {
            headers['X-API-Key'] = apiKey;
        }
        
        // 创建文档
        const docResponse = await fetch('/api/v1/documents/', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify({
                doc_id: docId,
                title: title,
                text: content,
                metadata: {
                    source: 'Web UI',
                    created_at: new Date().toISOString(),
                    api_key: apiKey,  // 传递API密钥到后端
                    base_url: baseUrl  // 传递Base URL到后端
                }
            })
        });
        
        if (!docResponse.ok) {
            throw new Error('创建文档失败');
        }
        
        // 执行分析
        const analysisResponse = await fetch('/api/v1/analysis/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                doc_id: docId,
                tasks: tasks
            })
        });
        
        if (!analysisResponse.ok) {
            throw new Error('分析文档失败');
        }
        
        const results = await analysisResponse.json();
        
        // 隐藏加载状态
        loading.style.display = 'none';
        
        // 显示结果
        displayResults(results);
        
        // 添加到历史记录
        addToHistory(docId, title, tasks);
        
        // 保存当前文档ID到sessionStorage，供可视化页面使用
        sessionStorage.setItem('currentDocId', docId);
        sessionStorage.setItem('fromAnalysis', 'true');
        
        showMessage('分析完成！', 'success');
        
    } catch (error) {
        loading.style.display = 'none';
        showMessage(`错误: ${error.message}`, 'error');
        console.error('分析错误:', error);
    }
}

// 显示分析结果
function displayResults(results) {
    resultsContainer.innerHTML = '';
    
    // 添加可视化按钮
    const visualizationBtn = document.createElement('div');
    visualizationBtn.className = 'visualization-btn-container';
    visualizationBtn.innerHTML = `
        <button class="viz-btn" onclick="switchToVisualization()">
            📊 查看数据可视化
        </button>
    `;
    resultsContainer.appendChild(visualizationBtn);
    
    for (const [taskType, result] of Object.entries(results)) {
        const resultCard = document.createElement('div');
        resultCard.className = 'result-card';
        
        const taskName = getTaskName(taskType);
        
        resultCard.innerHTML = `
            <h3>${taskName}</h3>
            <div class="result-content">
                <pre>${formatResult(result)}</pre>
            </div>
        `;
        
        resultsContainer.appendChild(resultCard);
    }
}

// 格式化结果
function formatResult(result) {
    if (result.success === false) {
        return `分析失败: ${result.error || '未知错误'}`;
    }
    
    if (result.result) {
        if (result.result.raw_analysis) {
            return result.result.raw_analysis;
        }
        return JSON.stringify(result.result, null, 2);
    }
    
    return JSON.stringify(result, null, 2);
}

// 获取任务名称
function getTaskName(taskType) {
    const taskNames = {
        'actor_relation': '行为者与关系分析',
        'role_framing': '角色塑造检测',
        'problem_scope': '问题范围策略',
        'causal_mechanism': '因果机制分析'
    };
    return taskNames[taskType] || taskType;
}

// 添加到历史记录
function addToHistory(docId, title, tasks) {
    const historyItem = {
        id: docId,
        title: title,
        tasks: tasks,
        date: new Date().toLocaleString('zh-CN')
    };
    
    historyData.unshift(historyItem);
    
    // 只保留最近10条记录
    if (historyData.length > 10) {
        historyData = historyData.slice(0, 10);
    }
    
    // 保存到本地存储
    localStorage.setItem('analysisHistory', JSON.stringify(historyData));
    
    // 更新显示
    updateHistoryDisplay();
}

// 加载历史记录
function loadHistory() {
    const stored = localStorage.getItem('analysisHistory');
    if (stored) {
        try {
            historyData = JSON.parse(stored);
            updateHistoryDisplay();
        } catch (e) {
            console.error('加载历史记录失败:', e);
        }
    }
}

// 更新历史记录显示
function updateHistoryDisplay() {
    if (historyData.length === 0) {
        historyList.innerHTML = '<p class="empty-state">暂无历史记录</p>';
        return;
    }
    
    historyList.innerHTML = '';
    
    historyData.forEach(item => {
        const historyItem = document.createElement('div');
        historyItem.className = 'history-item';
        historyItem.innerHTML = `
            <h4>${item.title}</h4>
            <div class="date">${item.date}</div>
        `;
        
        historyItem.addEventListener('click', () => {
            loadHistoryItem(item.id);
        });
        
        historyList.appendChild(historyItem);
    });
}

// 加载历史记录项
async function loadHistoryItem(docId) {
    try {
        const response = await fetch(`/api/v1/analysis/results/${docId}`);
        if (response.ok) {
            const results = await response.json();
            resultsSection.style.display = 'block';
            displayResults(results.results);
            showMessage('已加载历史记录', 'success');
        } else {
            showMessage('无法加载历史记录', 'error');
        }
    } catch (error) {
        showMessage(`加载失败: ${error.message}`, 'error');
    }
}

// 切换到数据可视化页面
function switchToVisualization() {
    // 获取当前分析的文档ID
    const currentDocId = historyData.length > 0 ? historyData[0].id : null;
    
    if (currentDocId) {
        // 保存当前文档ID到sessionStorage，供可视化页面使用
        sessionStorage.setItem('currentDocId', currentDocId);
        sessionStorage.setItem('fromAnalysis', 'true');
    }
    
    // 跳转到可视化页面
    window.location.href = '/static/visualization.html';
}

// 显示消息
function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
    messageDiv.textContent = message;
    
    // 插入到主内容区域顶部
    const main = document.querySelector('main');
    main.insertBefore(messageDiv, main.firstChild);
    
    // 3秒后自动移除
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}
