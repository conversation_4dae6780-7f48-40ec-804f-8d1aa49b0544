from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional, Dict, Any
from datetime import datetime
import logging

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Visualization"])


@router.get("/test")
async def test_endpoint():
    """测试端点"""
    return {"message": "可视化API正常工作", "status": "ok"}

@router.get("/dashboard")
async def get_dashboard_data():
    """获取仪表板数据"""
    try:
        return {
            "total_documents": 25,
            "total_analyses": 89,
            "recent_documents": [
                {
                    "doc_id": f"doc_{i}",
                    "title": f"文档 {i}",
                    "analysis_date": datetime.now().isoformat(),
                    "task_types": ["actor_relation", "role_framing"],
                    "total_actors": i * 2,
                    "total_relations": i * 3
                }
                for i in range(5)
            ],
            "system_stats": {
                "avg_analysis_time": 2.5,
                "success_rate": 0.95,
                "most_common_task": "actor_relation"
            }
        }
    except Exception as e:
        logger.error(f"获取仪表板数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取仪表板数据失败")


@router.get("/data/{doc_id}")
async def get_visualization_data(
    doc_id: str,
    task_types: Optional[List[str]] = Query(None, description="要可视化的任务类型")
):
    """获取文档的可视化数据"""
    try:
        # 示例数据
        sample_data = {
            "doc_id": doc_id,
            "title": f"文档 {doc_id}",
            "analysis_date": datetime.now().isoformat(),
            "task_types": ["actor_relation", "role_framing"],
            "charts": {
                "actor_relation": [
                    {
                        "type": "pie",
                        "title": "行为者类型分布",
                        "data": {
                            "labels": ["政府机构", "企业", "公众", "社会组织"],
                            "datasets": [{
                                "data": [5, 3, 2, 1],
                                "backgroundColor": ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0"]
                            }]
                        }
                    },
                    {
                        "type": "bar",
                        "title": "关系类型分布",
                        "data": {
                            "labels": ["监管关系", "合作关系", "对立关系", "服务关系"],
                            "datasets": [{
                                "label": "关系数量",
                                "data": [8, 4, 2, 3],
                                "backgroundColor": "#36A2EB"
                            }]
                        }
                    }
                ],
                "role_framing": [
                    {
                        "type": "pie",
                        "title": "角色分布",
                        "data": {
                            "labels": ["英雄", "受害者", "反派"],
                            "datasets": [{
                                "data": [3, 2, 1],
                                "backgroundColor": ["#28a745", "#ffc107", "#dc3545"]
                            }]
                        }
                    },
                    {
                        "type": "radar",
                        "title": "角色特征分析",
                        "data": {
                            "labels": ["积极性", "影响力", "描述详细度", "情感强度"],
                            "datasets": [
                                {
                                    "label": "英雄",
                                    "data": [85, 75, 80, 90],
                                    "borderColor": "#28a745",
                                    "backgroundColor": "rgba(40, 167, 69, 0.2)"
                                },
                                {
                                    "label": "受害者",
                                    "data": [30, 60, 70, 40],
                                    "borderColor": "#ffc107",
                                    "backgroundColor": "rgba(255, 193, 7, 0.2)"
                                }
                            ]
                        }
                    }
                ]
            },
            "summary": {
                "total_actors": 11,
                "total_relations": 17,
                "complexity_score": 0.8,
                "key_themes": ["政策监管", "企业发展", "公众参与"]
            }
        }
        
        return sample_data
        
    except Exception as e:
        logger.error(f"获取可视化数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取可视化数据失败")


@router.get("/summary/{doc_id}")
async def get_document_summary(doc_id: str):
    """获取文档的分析摘要"""
    try:
        return {
            "total_actors": 11,
            "total_relations": 17,
            "role_distribution": {
                "政府机构": 5,
                "企业": 3,
                "公众": 2,
                "社会组织": 1
            },
            "sentiment_score": 0.0,
            "complexity_score": 0.8,
            "key_themes": ["政策监管", "企业发展", "公众参与"]
        }
    except Exception as e:
        logger.error(f"获取摘要数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取摘要数据失败")


@router.get("/charts/{doc_id}")
async def get_document_charts(
    doc_id: str,
    task_types: Optional[List[str]] = Query(None, description="要可视化的任务类型")
):
    """获取文档的图表数据"""
    try:
        charts = {
            "actor_relation": [
                {
                    "type": "pie",
                    "title": "行为者类型分布",
                    "data": {
                        "labels": ["政府机构", "企业", "公众", "社会组织"],
                        "datasets": [{
                            "data": [5, 3, 2, 1],
                            "backgroundColor": ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0"]
                        }]
                    }
                }
            ],
            "role_framing": [
                {
                    "type": "pie",
                    "title": "角色分布",
                    "data": {
                        "labels": ["英雄", "受害者", "反派"],
                        "datasets": [{
                            "data": [3, 2, 1],
                            "backgroundColor": ["#28a745", "#ffc107", "#dc3545"]
                        }]
                    }
                }
            ]
        }
        
        return {"doc_id": doc_id, "charts": charts, "generated_at": datetime.now()}
        
    except Exception as e:
        logger.error(f"获取图表数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取图表数据失败")


@router.get("/compare/{doc_id1}/{doc_id2}")
async def compare_documents(doc_id1: str, doc_id2: str):
    """比较两个文档的分析结果"""
    try:
        return {
            "doc_id1": doc_id1,
            "doc_id2": doc_id2,
            "comparison": {
                "metrics_comparison": {
                    "actors": {
                        "doc1": 11,
                        "doc2": 8,
                        "difference": -3
                    },
                    "relations": {
                        "doc1": 17,
                        "doc2": 12,
                        "difference": -5
                    },
                    "complexity": {
                        "doc1": 0.8,
                        "doc2": 0.6,
                        "difference": -0.2
                    }
                },
                "role_distribution_comparison": {
                    "doc1": {"政府机构": 5, "企业": 3, "公众": 2, "社会组织": 1},
                    "doc2": {"政府机构": 3, "企业": 2, "公众": 2, "社会组织": 1}
                }
            },
            "generated_at": datetime.now()
        }
    except Exception as e:
        logger.error(f"文档比较失败: {e}")
        raise HTTPException(status_code=500, detail="文档比较失败")


@router.post("/export/{doc_id}")
async def export_visualization(
    doc_id: str,
    format: str = Query("json", description="导出格式: json, csv")
):
    """导出可视化数据"""
    try:
        if format == "json":
            return {
                "format": "json",
                "data": {
                    "doc_id": doc_id,
                    "exported_at": datetime.now().isoformat(),
                    "message": "示例导出数据"
                },
                "exported_at": datetime.now().isoformat()
            }
        elif format == "csv":
            return {
                "format": "csv",
                "data": "doc_id,title,analysis_date\ntest_doc,测试文档,2024-01-01",
                "exported_at": datetime.now().isoformat()
            }
        else:
            raise ValueError(f"不支持的导出格式: {format}")
            
    except Exception as e:
        logger.error(f"导出可视化数据失败: {e}")
        raise HTTPException(status_code=500, detail="导出可视化数据失败")