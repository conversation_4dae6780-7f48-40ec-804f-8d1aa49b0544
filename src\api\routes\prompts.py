from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Body
from typing import List, Optional
import logging

from src.models.schemas import (
    PromptTemplate, PromptTemplateCreate, PromptTemplateUpdate,
    PromptComparison, PromptComparisonRequest, PromptTestResult,
    TaskType
)
from src.services.prompt_service import PromptManagementService
from src.services.zhipu_client import get_zhipu_client

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Prompts"])


# 依赖注入
async def get_prompt_service() -> PromptManagementService:
    """获取提示词管理服务"""
    zhipu_client = await get_zhipu_client()
    return PromptManagementService(zhipu_client)


@router.get("/", response_model=List[PromptTemplate])
async def get_all_prompts(
    task_type: Optional[TaskType] = None,
    active_only: bool = True,
    service: PromptManagementService = Depends(get_prompt_service)
):
    """获取所有提示词模板"""
    try:
        if task_type:
            prompts = service.get_prompts_by_task_type(task_type)
        else:
            prompts = service.get_active_prompts() if active_only else list(service.prompts.values())
        
        return prompts
    except Exception as e:
        logger.error(f"获取提示词列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取提示词列表失败")


@router.get("/{prompt_id}", response_model=PromptTemplate)
async def get_prompt(
    prompt_id: str,
    service: PromptManagementService = Depends(get_prompt_service)
):
    """获取单个提示词模板"""
    prompt = service.get_prompt(prompt_id)
    if not prompt:
        raise HTTPException(status_code=404, detail="提示词不存在")
    return prompt


@router.post("/", response_model=PromptTemplate)
async def create_prompt(
    prompt_create: PromptTemplateCreate,
    service: PromptManagementService = Depends(get_prompt_service)
):
    """创建新的提示词模板"""
    try:
        prompt = service.create_prompt(prompt_create)
        return prompt
    except Exception as e:
        logger.error(f"创建提示词失败: {e}")
        raise HTTPException(status_code=500, detail="创建提示词失败")


@router.put("/{prompt_id}", response_model=PromptTemplate)
async def update_prompt(
    prompt_id: str,
    prompt_update: PromptTemplateUpdate,
    service: PromptManagementService = Depends(get_prompt_service)
):
    """更新提示词模板"""
    prompt = service.update_prompt(prompt_id, prompt_update)
    if not prompt:
        raise HTTPException(status_code=404, detail="提示词不存在")
    return prompt


@router.delete("/{prompt_id}")
async def delete_prompt(
    prompt_id: str,
    service: PromptManagementService = Depends(get_prompt_service)
):
    """删除提示词模板"""
    success = service.delete_prompt(prompt_id)
    if not success:
        raise HTTPException(status_code=404, detail="提示词不存在")
    return {"message": "提示词删除成功"}


@router.post("/compare", response_model=PromptComparison)
async def compare_prompts(
    request: PromptComparisonRequest,
    service: PromptManagementService = Depends(get_prompt_service)
):
    """比较两个提示词模板"""
    try:
        comparison = await service.compare_prompts(request)
        return comparison
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"比较提示词失败: {e}")
        raise HTTPException(status_code=500, detail="比较提示词失败")


@router.post("/test/{prompt_id}", response_model=PromptTestResult)
async def test_prompt(
    prompt_id: str,
    request: dict,
    service: PromptManagementService = Depends(get_prompt_service)
):
    """测试单个提示词"""
    try:
        test_document = request.get("test_document", "")
        document_id = request.get("document_id", "test_doc")
        result = await service.test_prompt(prompt_id, test_document, document_id)
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"测试提示词失败: {e}")
        raise HTTPException(status_code=500, detail="测试提示词失败")


@router.get("/task/{task_type}", response_model=List[PromptTemplate])
async def get_prompts_by_task_type(
    task_type: TaskType,
    service: PromptManagementService = Depends(get_prompt_service)
):
    """根据任务类型获取提示词"""
    prompts = service.get_prompts_by_task_type(task_type)
    return prompts


@router.post("/import")
async def import_prompts(
    file: UploadFile = File(...),
    service: PromptManagementService = Depends(get_prompt_service)
):
    """导入提示词模板"""
    try:
        content = await file.read()
        # 这里可以实现导入逻辑
        return {"message": "导入成功", "imported_count": 0}
    except Exception as e:
        logger.error(f"导入提示词失败: {e}")
        raise HTTPException(status_code=500, detail="导入提示词失败")


@router.get("/export/{prompt_id}")
async def export_prompt(
    prompt_id: str,
    service: PromptManagementService = Depends(get_prompt_service)
):
    """导出提示词模板"""
    prompt = service.get_prompt(prompt_id)
    if not prompt:
        raise HTTPException(status_code=404, detail="提示词不存在")
    
    return {
        "prompt": prompt.dict(),
        "exported_at": "2024-01-01T00:00:00"
    }