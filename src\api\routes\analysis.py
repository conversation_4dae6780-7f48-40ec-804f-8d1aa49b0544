from fastapi import APIRouter, HTTPException, Depends, status
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime
import uuid
import asyncio

from src.models.schemas import AnalysisRequest, AnalysisResponse, AnalysisResult, TaskType
from src.services.document_service import DocumentService, get_document_service
from src.services.analysis_service import AnalysisService, get_analysis_service
from src.core.auth import auth_deps
from src.core.validation import input_validator

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/", response_model=AnalysisResponse)
async def analyze_document(
    request: AnalysisRequest,
    document_service: DocumentService = Depends(get_document_service),
    analysis_service: AnalysisService = Depends(get_analysis_service),
    current_user: Dict[str, Any] = Depends(auth_deps.get_current_user)
):
    """分析文档"""
    try:
        # 验证任务类型
        validated_tasks = input_validator.validate_task_types([task.value for task in request.tasks])
        
        # 验证文档ID
        if not request.doc_id or not request.doc_id.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文档ID不能为空"
            )
        
        # 获取文档
        document = await document_service.get_document(request.doc_id)
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"文档 {request.doc_id} 不存在"
            )
        
        # 执行分析任务
        results = {}
        for task in request.tasks:
            result = await analysis_service.analyze(document.text, task)
            results[task] = result
        
        # 保存分析结果
        await analysis_service.save_results(request.doc_id, results)
        
        # 构建响应
        response = AnalysisResponse(
            doc_id=request.doc_id,
            results=results,
            created_at=datetime.now()
        )
        
        logger.info(f"用户 {current_user['username']} 分析文档 {request.doc_id}，执行了 {len(request.tasks)} 个任务")
        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分析文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分析文档失败: {str(e)}"
        )


@router.get("/results/{doc_id}")
async def get_analysis_results(
    doc_id: str,
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """获取文档分析结果"""
    try:
        results = await analysis_service.get_results(doc_id)
        if not results:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"文档 {doc_id} 的分析结果不存在"
            )
        
        # 返回简单的响应格式 - fixed version
        from datetime import datetime
        response = {
            "doc_id": doc_id,
            "results": results,
            "created_at": datetime.now().isoformat(),
            "success": True
        }
        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分析结果失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取分析结果失败: {str(e)}"
        )


@router.get("/tasks", response_model=List[Dict[str, str]])
async def list_analysis_tasks():
    """获取所有可用的分析任务"""
    tasks = []
    for task in TaskType:
        tasks.append({
            "id": task.value,
            "name": task.name,
            "description": get_task_description(task)
        })
    return tasks


def get_task_description(task: TaskType) -> str:
    """获取任务描述"""
    descriptions = {
        TaskType.ACTOR_RELATION: "识别文档中的行为者及其相互关系",
        TaskType.ROLE_FRAMING: "检测英雄/受害者/反派的角色塑造模式",
        TaskType.PROBLEM_SCOPE: "识别问题扩大化或缩小化策略",
        TaskType.CAUSAL_MECHANISM: "分析问题的因果归因方式"
    }
    return descriptions.get(task, "未知任务")
