from pydantic import BaseModel, Field, field_validator, EmailStr
from typing import List, Dict, Any, Optional
from enum import Enum
from datetime import datetime
from email_validator import validate_email, EmailNotValidError


class TaskType(str, Enum):
    """分析任务类型枚举"""
    ACTOR_RELATION = "actor_relation"     # 行为者与关系提取
    ROLE_FRAMING = "role_framing"         # 角色塑造检测
    PROBLEM_SCOPE = "problem_scope"       # 问题范围策略检测
    CAUSAL_MECHANISM = "causal_mechanism" # 因果机制检测


class DocumentBase(BaseModel):
    """文档基础模型"""
    title: str = Field(..., description="文档标题")
    text: str = Field(..., description="文档正文内容")


class DocumentCreate(DocumentBase):
    """创建文档请求模型"""
    doc_id: Optional[str] = Field(None, description="文档ID，不提供则自动生成")
    metadata: Optional[Dict[str, Any]] = Field(default={}, description="文档元数据")


class DocumentResponse(DocumentBase):
    """文档响应模型"""
    doc_id: str = Field(..., description="文档ID")
    metadata: Dict[str, Any] = Field(default={}, description="文档元数据")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    word_count: int = Field(..., description="文档字数")

    class Config:
        from_attributes = True


class ActorRelationResult(BaseModel):
    """行为者与关系分析结果"""
    actors: List[Dict[str, Any]] = Field(..., description="文档中识别的行为者列表")
    relations: List[Dict[str, Any]] = Field(..., description="行为者之间的关系")
    key_findings: List[str] = Field(..., description="关键发现")


class RoleFramingResult(BaseModel):
    """角色塑造分析结果"""
    heroes: List[Dict[str, Any]] = Field(..., description="英雄角色")
    victims: List[Dict[str, Any]] = Field(..., description="受害者角色")
    villains: List[Dict[str, Any]] = Field(..., description="反派角色")
    narratives: List[Dict[str, Any]] = Field(..., description="叙事策略")
    key_findings: List[str] = Field(..., description="关键发现")


class ProblemScopeResult(BaseModel):
    """问题范围策略分析结果"""
    expansion_strategies: List[Dict[str, Any]] = Field(..., description="扩大化策略")
    reduction_strategies: List[Dict[str, Any]] = Field(..., description="缩小化策略")
    framing_patterns: List[Dict[str, Any]] = Field(..., description="框架模式")
    key_findings: List[str] = Field(..., description="关键发现")


class CausalMechanismResult(BaseModel):
    """因果机制分析结果"""
    causal_chains: List[Dict[str, Any]] = Field(..., description="因果链")
    attribution_patterns: List[Dict[str, Any]] = Field(..., description="归因模式")
    responsibility_framing: Dict[str, Any] = Field(..., description="责任框架")
    key_findings: List[str] = Field(..., description="关键发现")


class AnalysisResult(BaseModel):
    """分析结果模型"""
    task_type: TaskType = Field(..., description="分析任务类型")
    result: Dict[str, Any] = Field(..., description="分析结果")
    success: bool = Field(..., description="是否成功")
    error: Optional[str] = Field(None, description="错误信息")


class AnalysisRequest(BaseModel):
    """分析请求模型"""
    doc_id: str = Field(..., description="文档ID")
    tasks: List[TaskType] = Field(..., description="要执行的分析任务列表")


class AnalysisResponse(BaseModel):
    """分析响应模型"""
    doc_id: str = Field(..., description="文档ID")
    results: Dict[TaskType, AnalysisResult] = Field(..., description="各任务的分析结果")
    created_at: datetime = Field(..., description="分析时间")

    class Config:
        from_attributes = True


class BatchAnalysisRequest(BaseModel):
    """批量分析请求"""
    document_ids: List[str] = Field(..., description="文档ID列表")
    tasks: List[TaskType] = Field(..., description="要执行的分析任务列表")


class BatchAnalysisResponse(BaseModel):
    """批量分析响应"""
    job_id: str = Field(..., description="批处理作业ID")
    status: str = Field(..., description="作业状态")
    total: int = Field(..., description="总文档数")
    completed: int = Field(..., description="已完成数量")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class VisualizationData(BaseModel):
    """可视化数据模型"""
    doc_id: str = Field(..., description="文档ID")
    title: str = Field(..., description="文档标题")
    analysis_date: datetime = Field(..., description="分析时间")
    task_types: List[TaskType] = Field(..., description="分析任务类型")
    charts: Dict[str, Any] = Field(..., description="图表数据")
    summary: Dict[str, Any] = Field(..., description="分析摘要")


class ChartData(BaseModel):
    """图表数据模型"""
    type: str = Field(..., description="图表类型: bar, line, pie, radar, network, etc.")
    title: str = Field(..., description="图表标题")
    data: Dict[str, Any] = Field(..., description="图表数据")
    options: Dict[str, Any] = Field(default={}, description="图表配置选项")


class SummaryMetrics(BaseModel):
    """摘要指标模型"""
    total_actors: int = Field(default=0, description="行为者总数")
    total_relations: int = Field(default=0, description="关系总数")
    role_distribution: Dict[str, int] = Field(default={}, description="角色分布")
    sentiment_score: float = Field(default=0.0, description="情感评分")
    complexity_score: float = Field(default=0.0, description="复杂度评分")
    key_themes: List[str] = Field(default=[], description="关键主题")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")


class PromptTemplate(BaseModel):
    """提示词模板模型"""
    id: str = Field(..., description="提示词ID")
    task_type: TaskType = Field(..., description="任务类型")
    name: str = Field(..., description="提示词名称")
    description: str = Field(..., description="提示词描述")
    template: str = Field(..., description="提示词模板内容")
    version: str = Field(..., description="版本号")
    is_active: bool = Field(True, description="是否激活使用")
    is_default: bool = Field(False, description="是否为默认模板")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    created_by: str = Field("system", description="创建者")
    tags: List[str] = Field(default=[], description="标签")
    performance_score: Optional[float] = Field(None, description="性能评分")


class PromptTemplateCreate(BaseModel):
    """创建提示词模板请求"""
    task_type: TaskType = Field(..., description="任务类型")
    name: str = Field(..., description="提示词名称")
    description: str = Field(..., description="提示词描述")
    template: str = Field(..., description="提示词模板内容")
    tags: List[str] = Field(default=[], description="标签")


class PromptTemplateUpdate(BaseModel):
    """更新提示词模板请求"""
    name: Optional[str] = Field(None, description="提示词名称")
    description: Optional[str] = Field(None, description="提示词描述")
    template: Optional[str] = Field(None, description="提示词模板内容")
    tags: Optional[List[str]] = Field(None, description="标签")
    is_active: Optional[bool] = Field(None, description="是否激活使用")


class PromptTemplateResponse(BaseModel):
    """提示词模板响应"""
    id: str = Field(..., description="提示词ID")
    task_type: TaskType = Field(..., description="任务类型")
    name: str = Field(..., description="提示词名称")
    description: str = Field(..., description="提示词描述")
    template: str = Field(..., description="提示词模板内容")
    version: str = Field(..., description="版本号")
    is_active: bool = Field(..., description="是否激活使用")
    is_default: bool = Field(..., description="是否为默认模板")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    created_by: str = Field(..., description="创建者")
    tags: List[str] = Field(..., description="标签")
    performance_score: Optional[float] = Field(None, description="性能评分")

    class Config:
        from_attributes = True


class PromptComparison(BaseModel):
    """提示词比较结果"""
    prompt1_id: str = Field(..., description="提示词1 ID")
    prompt2_id: str = Field(..., description="提示词2 ID")
    comparison_criteria: List[str] = Field(..., description="比较标准")
    differences: Dict[str, Any] = Field(..., description="差异分析")
    similarity_score: float = Field(..., description="相似度评分")
    recommendation: str = Field(..., description="推荐建议")
    compared_at: datetime = Field(..., description="比较时间")


class PromptComparisonRequest(BaseModel):
    """提示词比较请求"""
    prompt1_id: str = Field(..., description="提示词1 ID")
    prompt2_id: str = Field(..., description="提示词2 ID")
    test_document: str = Field(..., description="测试文档内容")
    comparison_criteria: List[str] = Field(default=["length", "structure", "clarity", "effectiveness"], description="比较标准")


class PromptTestResult(BaseModel):
    """提示词测试结果"""
    prompt_id: str = Field(..., description="提示词ID")
    document_id: str = Field(..., description="测试文档ID")
    response_time: float = Field(..., description="响应时间（秒）")
    result_quality: float = Field(..., description="结果质量评分")
    json_validity: bool = Field(..., description="JSON格式是否有效")
    completeness_score: float = Field(..., description="完整性评分")
    error_message: Optional[str] = Field(None, description="错误信息")
    tested_at: datetime = Field(..., description="测试时间")


# 身份验证相关模型
class Token(BaseModel):
    """令牌模型"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(..., description="令牌类型")


class TokenData(BaseModel):
    """令牌数据模型"""
    username: Optional[str] = Field(None, description="用户名")
    user_id: Optional[str] = Field(None, description="用户ID")
    role: Optional[str] = Field(None, description="用户角色")
    permissions: Optional[List[str]] = Field(default=[], description="用户权限")


class UserBase(BaseModel):
    """用户基础模型"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱地址")
    role: str = Field(default="user", description="用户角色")
    is_active: bool = Field(default=True, description="是否激活")
    permissions: List[str] = Field(default=[], description="用户权限")

    @field_validator('username')
    @classmethod
    def validate_username(cls, v):
        """验证用户名"""
        if not v.isalnum() and '_' not in v:
            raise ValueError('用户名只能包含字母、数字和下划线')
        return v

    @field_validator('role')
    @classmethod
    def validate_role(cls, v):
        """验证用户角色"""
        allowed_roles = ['user', 'admin', 'moderator']
        if v not in allowed_roles:
            raise ValueError(f'角色必须是: {", ".join(allowed_roles)}')
        return v


class UserCreate(UserBase):
    """创建用户模型"""
    password: str = Field(..., min_length=8, description="密码")

    @field_validator('password')
    @classmethod
    def validate_password(cls, v):
        """验证密码强度"""
        if len(v) < 8:
            raise ValueError('密码长度至少8位')
        if not any(c.isupper() for c in v):
            raise ValueError('密码必须包含至少一个大写字母')
        if not any(c.islower() for c in v):
            raise ValueError('密码必须包含至少一个小写字母')
        if not any(c.isdigit() for c in v):
            raise ValueError('密码必须包含至少一个数字')
        return v


class UserUpdate(BaseModel):
    """更新用户模型"""
    username: Optional[str] = Field(None, min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱地址")
    role: Optional[str] = Field(None, description="用户角色")
    is_active: Optional[bool] = Field(None, description="是否激活")
    permissions: Optional[List[str]] = Field(None, description="用户权限")


class UserResponse(UserBase):
    """用户响应模型"""
    user_id: str = Field(..., description="用户ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class UserLogin(BaseModel):
    """用户登录模型"""
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


class LoginResponse(BaseModel):
    """登录响应模型"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(..., description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")
    user: Dict[str, Any] = Field(..., description="用户信息")


class APIKeyResponse(BaseModel):
    """API密钥响应模型"""
    api_key: str = Field(..., description="API密钥")
    description: str = Field(..., description="描述信息")


class UserListResponse(BaseModel):
    """用户列表响应模型"""
    users: List[UserResponse] = Field(..., description="用户列表")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="页码")
    page_size: int = Field(..., description="每页大小")


class PermissionRequest(BaseModel):
    """权限请求模型"""
    resource: str = Field(..., description="资源")
    action: str = Field(..., description="操作")
    conditions: Optional[Dict[str, Any]] = Field(default={}, description="条件")


class PermissionResponse(BaseModel):
    """权限响应模型"""
    granted: bool = Field(..., description="是否授权")
    message: Optional[str] = Field(None, description="消息")
    conditions: Optional[Dict[str, Any]] = Field(default={}, description="条件")
